class OllamaChat {
    constructor() {
        this.currentModel = null;
        this.mcpServers = {};
        this.selectedImage = null;

        this.initElements();
        this.setupEventListeners();
        this.loadModels();
        this.loadMCPServers();
        this.loadChatHistory();
    }

    initElements() {
        this.modelSelect = document.getElementById('modelSelect');
        this.mcpBtn = document.getElementById('mcpBtn');
        this.messages = document.getElementById('messages');
        this.messageInput = document.getElementById('messageInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.sendBtn = document.getElementById('sendBtn');
        this.mcpModal = document.getElementById('mcpModal');
        this.closeMcpModal = document.getElementById('closeMcpModal');
        this.mcpServersEl = document.getElementById('mcpServers');

        // Add server elements
        this.serverUrl = document.getElementById('serverUrl');
        this.serverName = document.getElementById('serverName');
        this.addServerBtn = document.getElementById('addServerBtn');

        // History elements
        this.historyBtn = document.getElementById('historyBtn');
        this.historyModal = document.getElementById('historyModal');
        this.closeHistoryModal = document.getElementById('closeHistoryModal');
        this.historyList = document.getElementById('historyList');
        this.clearHistoryBtn = document.getElementById('clearHistoryBtn');

        // Image elements
        this.attachBtn = document.getElementById('attachBtn');
        this.imageInput = document.getElementById('imageInput');
        this.imagePreview = document.getElementById('imagePreview');
    }

    setupEventListeners() {
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.searchBtn.addEventListener('click', () => this.searchMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.modelSelect.addEventListener('change', (e) => {
            this.currentModel = e.target.value;
        });

        this.mcpBtn.addEventListener('click', () => this.openMCPModal());
        this.closeMcpModal.addEventListener('click', () => this.closeMCPModal());

        this.mcpModal.addEventListener('click', (e) => {
            if (e.target === this.mcpModal) {
                this.closeMCPModal();
            }
        });

        // Add server functionality
        this.addServerBtn.addEventListener('click', () => this.addNewServer());

        // History functionality
        this.historyBtn.addEventListener('click', () => this.openHistoryModal());
        this.closeHistoryModal.addEventListener('click', () => this.closeHistoryModalHandler());
        this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());

        this.historyModal.addEventListener('click', (e) => {
            if (e.target === this.historyModal) {
                this.closeHistoryModalHandler();
            }
        });

        // Image functionality
        this.attachBtn.addEventListener('click', () => this.imageInput.click());
        this.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));
    }

    async loadModels() {
        try {
            const response = await fetch('/api/models');
            const data = await response.json();

            this.modelSelect.innerHTML = '<option value="">Select Model...</option>';

            if (data.success && data.models) {
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = model.name;
                    this.modelSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading models:', error);
        }
    }

    async loadMCPServers() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.success && data.mcp_servers) {
                this.mcpServers = data.mcp_servers;
                this.renderMCPServers();
            }
        } catch (error) {
            console.error('Error loading MCP servers:', error);
        }
    }

    renderMCPServers() {
        this.mcpServersEl.innerHTML = '';

        Object.entries(this.mcpServers).forEach(([name, server]) => {
            const serverEl = document.createElement('div');
            serverEl.className = 'mcp-server';

            const isConnected = server.connected;
            const statusClass = isConnected ? 'connected' : 'disconnected';
            const statusText = isConnected ? 'Connected' : 'Disconnected';
            const buttonText = isConnected ? 'Disconnect' : 'Connect';
            const buttonClass = isConnected ? 'disconnect' : 'connect';

            const deleteBtn = server.custom ? `<button class="delete-btn" data-server="${name}">Delete</button>` : '';

            serverEl.innerHTML = `
                <div class="mcp-server-header">
                    <div class="mcp-server-name">${this.formatServerName(name)}</div>
                    <div class="mcp-server-status ${statusClass}">${statusText}</div>
                </div>
                <div class="mcp-server-description">${server.description}</div>
                <div class="mcp-server-tools">Tools: ${server.tools.join(', ')}</div>
                <div class="mcp-server-actions">
                    <button class="connect-btn ${buttonClass}" data-server="${name}">
                        ${buttonText}
                    </button>
                    ${deleteBtn}
                </div>
            `;

            const connectBtn = serverEl.querySelector('.connect-btn');
            connectBtn.addEventListener('click', () => this.toggleMCPConnection(name));

            const deleteBtn = serverEl.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => this.deleteServer(name));
            }

            this.mcpServersEl.appendChild(serverEl);
        });
    }

    formatServerName(name) {
        return name.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    async toggleMCPConnection(serverName) {
        const server = this.mcpServers[serverName];
        if (!server) return;

        const connectBtn = document.querySelector(`[data-server="${serverName}"]`);
        connectBtn.disabled = true;
        connectBtn.innerHTML = '<div class="loading"></div>';

        try {
            if (server.connected) {
                console.log('Disconnect not implemented yet');
            } else {
                const response = await fetch(`/api/mcp/connect/${serverName}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    this.loadMCPServers();
                }
            }
        } catch (error) {
            console.error('Error toggling MCP connection:', error);
        } finally {
            connectBtn.disabled = false;
        }
    }

    async addNewServer() {
        const url = this.serverUrl.value.trim();
        const name = this.serverName.value.trim();

        if (!url) {
            alert('Please enter a server URL');
            return;
        }

        // Generate server ID from URL
        const serverId = this.generateServerId(url);
        const serverName = name || this.extractServerName(url);

        this.addServerBtn.disabled = true;
        this.addServerBtn.textContent = 'Adding...';

        try {
            const response = await fetch('/api/mcp/add-server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: serverId,
                    name: serverName,
                    url: url,
                    description: `Custom MCP server: ${serverName}`
                })
            });

            const result = await response.json();

            if (result.success) {
                this.serverUrl.value = '';
                this.serverName.value = '';
                this.loadMCPServers();
                alert(`Server "${serverName}" added successfully!`);
            } else {
                alert(`Failed to add server: ${result.error}`);
            }
        } catch (error) {
            alert(`Error adding server: ${error.message}`);
        } finally {
            this.addServerBtn.disabled = false;
            this.addServerBtn.textContent = 'Add Server';
        }
    }

    generateServerId(url) {
        // Extract a clean ID from the URL
        const match = url.match(/server\.smithery\.ai\/(@[^\/]+\/[^\/]+)/);
        if (match) {
            return match[1].replace(/[@\/]/g, '_').toLowerCase();
        }
        return 'custom_' + Date.now();
    }

    extractServerName(url) {
        // Extract server name from Smithery URL
        const match = url.match(/server\.smithery\.ai\/@[^\/]+\/([^\/]+)/);
        if (match) {
            return match[1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
        return 'Custom Server';
    }

    async deleteServer(serverName) {
        if (!confirm(`Are you sure you want to delete the server "${this.formatServerName(serverName)}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/mcp/delete-server/${serverName}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.loadMCPServers();
                alert(`Server "${this.formatServerName(serverName)}" deleted successfully!`);
            } else {
                alert(`Failed to delete server: ${result.error}`);
            }
        } catch (error) {
            alert(`Error deleting server: ${error.message}`);
        }
    }

    openMCPModal() {
        this.mcpModal.style.display = 'flex';
        this.loadMCPServers();
    }

    closeMCPModal() {
        this.mcpModal.style.display = 'none';
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || !this.currentModel) return;

        const hasImage = !!this.selectedImage;
        this.addMessage(message, 'user', true, hasImage);
        this.messageInput.value = '';
        this.sendBtn.disabled = true;

        const requestBody = {
            message: message,
            model: this.currentModel,
            stream: false
        };

        if (this.selectedImage) {
            requestBody.images = [this.selectedImage.split(',')[1]]; // Remove data:image/...;base64, prefix
        }

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();

            if (data.success) {
                this.addMessage(data.response, 'assistant', true);
            } else {
                this.addMessage(`Error: ${data.error}`, 'error', true);
            }
        } catch (error) {
            this.addMessage(`Error: ${error.message}`, 'error', true);
        } finally {
            this.sendBtn.disabled = false;
            this.removeImage(); // Clear image after sending
        }
    }

    async searchMessage() {
        const query = this.messageInput.value.trim();
        if (!query) return;

        this.addMessage(`🔍 Searching: ${query}`, 'user');
        this.messageInput.value = '';
        this.searchBtn.disabled = true;

        try {
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query: query
                })
            });

            const data = await response.json();

            if (data.success && data.result && data.result.length > 0) {
                const searchResult = data.result[0].text;
                this.addMessage(searchResult, 'assistant');

                if (data.demo_mode) {
                    this.addMessage('ℹ️ This is a demo response. Connect to real MCP servers for actual results.', 'info');
                }
            } else {
                this.addMessage(`Search error: ${data.error || 'No results found'}`, 'error');
            }
        } catch (error) {
            this.addMessage(`Search error: ${error.message}`, 'error');
        } finally {
            this.searchBtn.disabled = false;
        }
    }

    async loadChatHistory() {
        try {
            const response = await fetch('/api/chat/history');
            const data = await response.json();

            if (data.success && data.history.length > 0) {
                // Show last few messages from history
                const recentMessages = data.history.slice(-10);
                recentMessages.forEach(msg => {
                    this.addMessage(msg.content, msg.role, false);
                });
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }

    handleImageSelect(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.selectedImage = e.target.result;
                this.showImagePreview(file.name, e.target.result);
            };
            reader.readAsDataURL(file);
        }
    }

    showImagePreview(fileName, dataUrl) {
        this.imagePreview.innerHTML = `
            <div style="display: flex; align-items: center; gap: 1rem;">
                <img src="${dataUrl}" alt="${fileName}" style="max-width: 100px; max-height: 100px; border-radius: 4px;">
                <div>
                    <div style="font-weight: 500;">${fileName}</div>
                    <button class="remove-image" onclick="ollamaChat.removeImage()">Remove</button>
                </div>
            </div>
        `;
        this.imagePreview.style.display = 'block';
    }

    removeImage() {
        this.selectedImage = null;
        this.imagePreview.style.display = 'none';
        this.imageInput.value = '';
    }

    addMessage(content, type, saveToHistory = false, hasImage = false) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = content;

        if (hasImage && type === 'user') {
            const imageIcon = document.createElement('span');
            imageIcon.textContent = ' 📷';
            imageIcon.style.opacity = '0.7';
            messageEl.appendChild(imageIcon);
        }

        this.messages.appendChild(messageEl);
        this.messages.scrollTop = this.messages.scrollHeight;
    }

    // History Modal Methods
    openHistoryModal() {
        this.historyModal.style.display = 'flex';
        setTimeout(() => {
            this.historyModal.classList.add('show');
        }, 10);
        this.loadHistoryList();
    }

    closeHistoryModalHandler() {
        this.historyModal.classList.remove('show');
        setTimeout(() => {
            this.historyModal.style.display = 'none';
        }, 300);
    }

    async loadHistoryList() {
        try {
            const response = await fetch('/api/chat/history');
            const data = await response.json();

            if (data.success) {
                this.renderHistoryList(data.history);
            }
        } catch (error) {
            console.error('Error loading history:', error);
        }
    }

    renderHistoryList(history) {
        this.historyList.innerHTML = '';

        if (history.length === 0) {
            this.historyList.innerHTML = '<p style="text-align: center; color: #666;">No chat history yet.</p>';
            return;
        }

        history.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = `history-item ${item.role}`;

            const timestamp = new Date(item.timestamp).toLocaleString();
            const hasImageIcon = item.has_image ? ' 📷' : '';
            const modelInfo = item.model ? ` (${item.model})` : '';

            historyItem.innerHTML = `
                <div class="history-header">
                    <span class="history-role">${item.role}${hasImageIcon}</span>
                    <span class="history-timestamp">${timestamp}</span>
                </div>
                <div class="history-content">${item.content}</div>
                ${item.model ? `<div class="history-meta">Model: ${item.model}</div>` : ''}
            `;

            this.historyList.appendChild(historyItem);
        });
    }

    async clearHistory() {
        if (!confirm('Are you sure you want to clear all chat history?')) {
            return;
        }

        try {
            const response = await fetch('/api/chat/clear', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.messages.innerHTML = '';
                this.loadHistoryList();
                alert('Chat history cleared successfully!');
            }
        } catch (error) {
            console.error('Error clearing history:', error);
            alert('Error clearing history');
        }
    }
}

// Global instance for onclick handlers
let ollamaChat;

document.addEventListener('DOMContentLoaded', () => {
    ollamaChat = new OllamaChat();
});
