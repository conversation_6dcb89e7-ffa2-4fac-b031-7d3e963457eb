class EnhancedOllamaChatApp {
  constructor() {
    this.currentModel = ""
    this.isConnected = false
    this.isGenerating = false
    this.messageCount = 0
    this.sessionStartTime = Date.now()
    this.attachedImages = []
    this.mcpServers = {}
    this.searchEnabled = false

    this.initializeElements()
    this.setupEventListeners()
    this.checkConnection()
    this.loadModels()
    this.loadMCPServers()
    this.startSessionTimer()
  }

  initializeElements() {
    // Status elements
    this.statusDot = document.getElementById("statusDot")
    this.statusText = document.getElementById("statusText")
    this.connectionIndicator = document.getElementById("connectionIndicator")

    // Model elements
    this.modelSelect = document.getElementById("modelSelect")
    this.refreshModels = document.getElementById("refreshModels")
    this.modelName = document.getElementById("modelName")
    this.modelDescription = document.getElementById("modelDescription")
    this.modelStats = document.getElementById("modelStats")
    this.activeModelIndicator = document.getElementById("activeModelIndicator")

    // Chat elements
    this.messagesContainer = document.getElementById("messagesContainer")
    this.messagesScroll = document.getElementById("messagesScroll")
    this.messageInput = document.getElementById("messageInput")
    this.sendButton = document.getElementById("sendButton")
    this.sendIcon = document.getElementById("sendIcon")
    this.sendSpinner = document.getElementById("sendSpinner")
    this.charCount = document.getElementById("charCount")

    // Action buttons
    this.clearChatBtn = document.getElementById("clearChatBtn")
    this.exportChatBtn = document.getElementById("exportChatBtn")
    this.attachBtn = document.getElementById("attachBtn")
    this.settingsBtn = document.getElementById("settingsBtn")
    this.fullscreenBtn = document.getElementById("fullscreenBtn")

    // Stats
    this.messageCountEl = document.getElementById("messageCount")
    this.sessionTimeEl = document.getElementById("sessionTime")

    // Settings
    this.temperatureSlider = document.getElementById("temperatureSlider")
    this.temperatureValue = document.getElementById("temperatureValue")
    this.maxTokensInput = document.getElementById("maxTokensInput")

    // Loading overlay
    this.loadingOverlay = document.getElementById("loadingOverlay")
    this.toastContainer = document.getElementById("toastContainer")

    // File input (hidden)
    this.createFileInput()
  }

  createFileInput() {
    this.fileInput = document.createElement("input")
    this.fileInput.type = "file"
    this.fileInput.accept = "image/*"
    this.fileInput.multiple = true
    this.fileInput.style.display = "none"
    document.body.appendChild(this.fileInput)
  }

  setupEventListeners() {
    // Model selection
    this.refreshModels.addEventListener("click", () => this.loadModels())
    this.modelSelect.addEventListener("change", (e) => this.selectModel(e.target.value))

    // Chat functionality
    this.sendButton.addEventListener("click", () => this.sendMessage())
    this.clearChatBtn.addEventListener("click", () => this.clearChat())
    this.exportChatBtn.addEventListener("click", () => this.exportChat())

    // File attachment
    this.attachBtn.addEventListener("click", () => this.fileInput.click())
    this.fileInput.addEventListener("change", (e) => this.handleFileUpload(e))

    // Settings
    this.temperatureSlider.addEventListener("input", (e) => {
      this.temperatureValue.textContent = e.target.value
    })

    // Fullscreen toggle
    this.fullscreenBtn.addEventListener("click", () => this.toggleFullscreen())

    // Input handling
    this.messageInput.addEventListener("keydown", (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
        e.preventDefault()
        this.sendMessage()
      }
    })

    this.messageInput.addEventListener("input", () => {
      this.autoResizeTextarea()
      this.updateSendButton()
      this.updateCharCount()
    })

    // Drag and drop for images
    this.setupDragAndDrop()
  }

  setupDragAndDrop() {
    const dropZone = this.messagesContainer

    dropZone.addEventListener("dragover", (e) => {
      e.preventDefault()
      dropZone.classList.add("drag-over")
    })

    dropZone.addEventListener("dragleave", (e) => {
      e.preventDefault()
      dropZone.classList.remove("drag-over")
    })

    dropZone.addEventListener("drop", (e) => {
      e.preventDefault()
      dropZone.classList.remove("drag-over")

      const files = Array.from(e.dataTransfer.files).filter((file) => file.type.startsWith("image/"))

      if (files.length > 0) {
        this.processImageFiles(files)
      }
    })
  }

  async checkConnection() {
    try {
      const response = await fetch("/api/status")
      const data = await response.json()
      this.isConnected = data.ollama_connected
      this.mcpServers = data.mcp_servers || {}
    } catch (error) {
      console.error("Connection check failed:", error)
      this.isConnected = false
    }
    this.updateConnectionStatus()
  }

  updateConnectionStatus() {
    if (this.isConnected) {
      this.statusDot.classList.remove("disconnected")
      this.statusText.textContent = "Connected"
      this.connectionIndicator.textContent = "Connected"
      this.connectionIndicator.className = "connection-indicator connected"
    } else {
      this.statusDot.classList.add("disconnected")
      this.statusText.textContent = "Disconnected"
      this.connectionIndicator.textContent = "Disconnected"
      this.connectionIndicator.className = "connection-indicator disconnected"
      this.showToast("Cannot connect to Ollama backend", "error")
    }
  }

  async loadModels() {
    this.setLoadingState(this.refreshModels, true)

    try {
      const response = await fetch("/api/models")
      const data = await response.json()

      if (data.success) {
        this.populateModelSelect(data.models)
        this.isConnected = true
        this.showToast("Models loaded successfully", "success")
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error("Error loading models:", error)
      this.isConnected = false
      this.showToast(`Failed to load models: ${error.message}`, "error")
    } finally {
      this.setLoadingState(this.refreshModels, false)
      this.updateConnectionStatus()
    }
  }

  async loadMCPServers() {
    try {
      const response = await fetch("/api/mcp/servers")
      const data = await response.json()

      if (data.success) {
        this.mcpServers = data.servers
        this.searchEnabled = this.mcpServers.duckduckgo?.connected || false
      }
    } catch (error) {
      console.error("Error loading MCP servers:", error)
    }
  }

  populateModelSelect(models) {
    this.modelSelect.innerHTML = '<option value="">Select a model...</option>'

    if (models.length === 0) {
      this.modelSelect.innerHTML = '<option value="">No models available</option>'
      return
    }

    models.forEach((model) => {
      const option = document.createElement("option")
      option.value = model.name
      option.textContent = model.name
      this.modelSelect.appendChild(option)
    })
  }

  selectModel(modelName) {
    this.currentModel = modelName

    if (modelName) {
      this.modelName.textContent = modelName
      this.modelDescription.textContent = "Ready to chat! Type your message below."
      this.activeModelIndicator.textContent = modelName
      this.modelStats.style.display = "block"

      // Enable input
      this.messageInput.disabled = false
      this.attachBtn.disabled = false
      this.messageInput.placeholder = "Type your message here... (⌘+Enter to send)"
    } else {
      this.modelName.textContent = "No Model Selected"
      this.modelDescription.textContent = "Choose a model to begin"
      this.activeModelIndicator.textContent = "No model selected"
      this.modelStats.style.display = "none"

      // Disable input
      this.messageInput.disabled = true
      this.attachBtn.disabled = true
      this.messageInput.placeholder = "Select a model to start chatting..."
    }

    this.updateSendButton()
  }

  async handleFileUpload(event) {
    const files = Array.from(event.target.files)
    await this.processImageFiles(files)
    event.target.value = "" // Reset file input
  }

  async processImageFiles(files) {
    for (const file of files) {
      if (!file.type.startsWith("image/")) {
        this.showToast(`${file.name} is not an image file`, "error")
        continue
      }

      if (file.size > 16 * 1024 * 1024) {
        this.showToast(`${file.name} is too large (max 16MB)`, "error")
        continue
      }

      try {
        const formData = new FormData()
        formData.append("file", file)

        const response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        })

        const data = await response.json()

        if (data.success) {
          this.attachedImages.push({
            name: file.name,
            data: data.image_data,
          })
          this.showAttachedImages()
          this.showToast(`${file.name} attached successfully`, "success")
        } else {
          throw new Error(data.error)
        }
      } catch (error) {
        console.error("Upload error:", error)
        this.showToast(`Failed to upload ${file.name}: ${error.message}`, "error")
      }
    }
  }

  showAttachedImages() {
    // Remove existing image preview
    const existingPreview = document.querySelector(".image-preview")
    if (existingPreview) {
      existingPreview.remove()
    }

    if (this.attachedImages.length === 0) return

    const preview = document.createElement("div")
    preview.className = "image-preview"
    preview.innerHTML = `
      <div class="preview-header">
        <span class="preview-title">Attached Images (${this.attachedImages.length})</span>
        <button class="preview-clear" onclick="app.clearAttachedImages()">Clear All</button>
      </div>
      <div class="preview-images">
        ${this.attachedImages
          .map(
            (img, index) => `
          <div class="preview-image">
            <img src="data:image/jpeg;base64,${img.data}" alt="${img.name}" />
            <button class="remove-image" onclick="app.removeAttachedImage(${index})">×</button>
            <span class="image-name">${img.name}</span>
          </div>
        `,
          )
          .join("")}
      </div>
    `

    this.messageInput.parentNode.insertBefore(preview, this.messageInput)
  }

  removeAttachedImage(index) {
    this.attachedImages.splice(index, 1)
    this.showAttachedImages()
  }

  clearAttachedImages() {
    this.attachedImages = []
    this.showAttachedImages()
  }

  async sendMessage() {
    const message = this.messageInput.value.trim()
    if (!message || !this.currentModel || this.isGenerating) return

    // Check if message contains search intent
    const searchQuery = this.extractSearchQuery(message)

    // Add user message with images if any
    this.addMessage("user", message, this.attachedImages.length > 0 ? [...this.attachedImages] : null)

    // Clear input and images
    this.messageInput.value = ""
    const imagesToSend = [...this.attachedImages]
    this.attachedImages = []
    this.showAttachedImages()
    this.autoResizeTextarea()
    this.updateSendButton()
    this.updateCharCount()
    this.updateMessageCount()

    // Set generating state
    this.isGenerating = true
    this.setLoadingState(this.sendButton, true)
    this.showTypingIndicator()

    try {
      const payload = {
        model: this.currentModel,
        message: message,
        stream: true,
        images: imagesToSend.map((img) => img.data),
      }

      // Add search query if detected
      if (searchQuery && this.searchEnabled) {
        payload.search_query = searchQuery
      }

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      await this.handleStreamingResponse(response)
    } catch (error) {
      console.error("Error sending message:", error)
      this.addMessage("error", `Failed to send message: ${error.message}`)
    } finally {
      this.hideTypingIndicator()
      this.isGenerating = false
      this.setLoadingState(this.sendButton, false)
      this.updateSendButton()
    }
  }

  extractSearchQuery(message) {
    // Simple search intent detection
    const searchPatterns = [
      /search for (.+)/i,
      /look up (.+)/i,
      /find information about (.+)/i,
      /what is (.+)/i,
      /tell me about (.+)/i,
    ]

    for (const pattern of searchPatterns) {
      const match = message.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  }

  async handleStreamingResponse(response) {
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let assistantMessage = ""
    let messageElement = null

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split("\n")

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.error) {
                throw new Error(data.message)
              }

              if (data.response) {
                assistantMessage += data.response

                if (!messageElement) {
                  messageElement = this.addMessage("assistant", assistantMessage)
                } else {
                  messageElement.querySelector(".message-content").textContent = assistantMessage
                }

                this.scrollToBottom()
              }
            } catch (e) {
              if (e instanceof SyntaxError) {
                continue
              }
              throw e
            }
          }
        }
      }

      if (assistantMessage) {
        this.updateMessageCount()
      }
    } catch (error) {
      console.error("Stream error:", error)
      this.addMessage("error", `Stream error: ${error.message}`)
    }
  }

  addMessage(type, content, images = null) {
    const messageDiv = document.createElement("div")
    messageDiv.className = `message ${type}`

    let imageHtml = ""
    if (images && images.length > 0) {
      imageHtml = `
        <div class="message-images">
          ${images
            .map(
              (img) => `
            <div class="message-image">
              <img src="data:image/jpeg;base64,${img.data}" alt="${img.name}" />
            </div>
          `,
            )
            .join("")}
        </div>
      `
    }

    messageDiv.innerHTML = `
      ${imageHtml}
      <div class="message-content">${content}</div>
      <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `

    // Remove welcome message if it exists
    const welcomeMessage = this.messagesScroll.querySelector(".welcome-message")
    if (welcomeMessage) {
      welcomeMessage.remove()
    }

    this.messagesScroll.appendChild(messageDiv)
    this.scrollToBottom()

    return messageDiv
  }

  showTypingIndicator() {
    const typingDiv = document.createElement("div")
    typingDiv.className = "typing-indicator"
    typingDiv.id = "typingIndicator"
    typingDiv.innerHTML = `
      <div class="typing-content">
        <div class="typing-dots">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
        <span class="typing-text">AI is thinking...</span>
      </div>
    `
    this.messagesScroll.appendChild(typingDiv)
    this.scrollToBottom()
  }

  hideTypingIndicator() {
    const indicator = document.getElementById("typingIndicator")
    if (indicator) indicator.remove()
  }

  clearChat() {
    this.messagesScroll.innerHTML = `
      <div class="welcome-message">
        <div class="welcome-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.60557 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="welcome-title">Chat Cleared</h3>
        <p class="welcome-description">Ready for a new conversation!</p>
      </div>
    `
    this.messageCount = 0
    this.updateMessageCount()
    this.showToast("Chat history cleared", "success")
  }

  exportChat() {
    const messages = Array.from(this.messagesScroll.querySelectorAll(".message")).map((msg) => ({
      type: msg.className.replace("message ", ""),
      content: msg.querySelector(".message-content").textContent,
      time: msg.querySelector(".message-time")?.textContent || "",
    }))

    const exportData = {
      model: this.currentModel,
      timestamp: new Date().toISOString(),
      messages: messages,
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `ollama-chat-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    this.showToast("Chat exported successfully", "success")
  }

  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }

  updateMessageCount() {
    const messages = this.messagesScroll.querySelectorAll(".message.user, .message.assistant")
    this.messageCount = messages.length
    this.messageCountEl.textContent = this.messageCount
  }

  updateCharCount() {
    const count = this.messageInput.value.length
    this.charCount.textContent = count
  }

  startSessionTimer() {
    setInterval(() => {
      const elapsed = Date.now() - this.sessionStartTime
      const minutes = Math.floor(elapsed / 60000)
      const seconds = Math.floor((elapsed % 60000) / 1000)
      this.sessionTimeEl.textContent = `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
    }, 1000)
  }

  autoResizeTextarea() {
    this.messageInput.style.height = "auto"
    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 200) + "px"
  }

  updateSendButton() {
    const hasText = this.messageInput.value.trim().length > 0
    const canSend = hasText && this.currentModel && !this.isGenerating
    this.sendButton.disabled = !canSend
  }

  setLoadingState(element, isLoading) {
    if (element === this.sendButton) {
      this.sendIcon.style.display = isLoading ? "none" : "block"
      this.sendSpinner.style.display = isLoading ? "block" : "none"
    } else {
      element.disabled = isLoading
    }
  }

  showToast(message, type = "info") {
    const toast = document.createElement("div")
    toast.className = `toast toast-${type}`
    toast.innerHTML = `
      <div class="toast-content">
        <span class="toast-message">${message}</span>
        <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `

    this.toastContainer.appendChild(toast)

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove()
      }
    }, 5000)

    // Animate in
    setTimeout(() => {
      toast.classList.add("show")
    }, 100)
  }

  scrollToBottom() {
    this.messagesScroll.scrollTop = this.messagesScroll.scrollHeight
  }
}

// Initialize the application
let app
document.addEventListener("DOMContentLoaded", () => {
  app = new EnhancedOllamaChatApp()
})
