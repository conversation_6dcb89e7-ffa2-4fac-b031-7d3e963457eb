* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.model-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    min-width: 200px;
}

.mcp-btn {
    padding: 0.5rem 1rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.mcp-btn:hover {
    background: #0056b3;
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.messages {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    padding: 1rem;
    border-radius: 8px;
    max-width: 80%;
}

.message.user {
    background: #007bff;
    color: white;
    align-self: flex-end;
}

.message.assistant {
    background: white;
    border: 1px solid #e0e0e0;
    align-self: flex-start;
}

.message.error {
    background: #dc3545;
    color: white;
    align-self: center;
}

.message.info {
    background: #17a2b8;
    color: white;
    align-self: center;
    font-size: 0.9rem;
}

/* Input Area */
.input-area {
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 1rem;
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    font-family: inherit;
}

.search-btn {
    padding: 0.75rem;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    height: 44px;
    min-width: 44px;
    font-size: 1.2rem;
}

.search-btn:hover {
    background: #218838;
}

.search-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.send-btn {
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    height: 44px;
}

.send-btn:hover {
    background: #0056b3;
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

/* MCP Servers */
.mcp-servers {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mcp-server {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
}

.mcp-server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.mcp-server-name {
    font-weight: 600;
    color: #333;
}

.mcp-server-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.mcp-server-status.connected {
    background: #d4edda;
    color: #155724;
}

.mcp-server-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.mcp-server-description {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.mcp-server-tools {
    font-size: 0.875rem;
    color: #666;
}

.mcp-server-actions {
    margin-top: 1rem;
}

.connect-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.connect-btn.connect {
    background: #28a745;
    color: white;
}

.connect-btn.disconnect {
    background: #dc3545;
    color: white;
}

.connect-btn:hover {
    opacity: 0.9;
}

.connect-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Loading */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .message {
        max-width: 95%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}