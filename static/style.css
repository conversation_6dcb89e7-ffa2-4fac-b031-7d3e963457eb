:root {
  /* Color Palette - Professional Enterprise Theme */
  --color-primary: #0066cc;
  --color-primary-hover: #0052a3;
  --color-primary-light: #e6f3ff;
  --color-secondary: #6366f1;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Background Colors */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  --bg-sidebar: #fafbfc;
  --bg-nav: var(--color-white);
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-500);
  --text-inverse: var(--color-white);
  --text-muted: var(--color-gray-400);

  /* Border Colors */
  --border-primary: var(--color-gray-200);
  --border-secondary: var(--color-gray-300);
  --border-focus: var(--color-primary);

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 0.75rem;
  --space-lg: 1rem;
  --space-xl: 1.25rem;
  --space-2xl: 1.5rem;
  --space-3xl: 2rem;
  --space-4xl: 2.5rem;
  --space-5xl: 3rem;

  /* Typography */
  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
    sans-serif;
  --font-family-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;

  /* Layout */
  --sidebar-width: 320px;
  --header-height: 64px;
  --input-height: 120px;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  overflow: hidden;
}

/* App Shell Layout */
.app-shell {
  display: flex;
  height: 100vh;
  background: var(--bg-secondary);
}

/* Navigation Sidebar */
.nav-sidebar {
  width: var(--sidebar-width);
  background: var(--bg-nav);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.nav-header {
  padding: var(--space-2xl);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-primary);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.brand-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.brand-text {
  flex: 1;
}

.brand-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  margin: 0;
}

.brand-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.nav-status {
  margin-top: var(--space-sm);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--color-success);
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background: var(--color-error);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.status-text {
  color: var(--text-secondary);
}

/* Navigation Content */
.nav-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.nav-section {
  margin-bottom: var(--space-4xl);
}

.nav-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.section-action {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.section-action:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-xl);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.select-wrapper {
  position: relative;
}

.form-select {
  width: 100%;
  height: 44px;
  padding: 0 var(--space-md);
  padding-right: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  appearance: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.form-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.select-icon {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 var(--space-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  font-size: var(--text-sm);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

/* Slider */
.slider-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.form-slider {
  flex: 1;
  height: 6px;
  border-radius: var(--radius-full);
  background: var(--bg-tertiary);
  outline: none;
  appearance: none;
  cursor: pointer;
}

.form-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  background: var(--color-primary);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.form-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.slider-value {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  min-width: 32px;
  text-align: center;
}

/* Model Info Card */
.model-info-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin-top: var(--space-xl);
}

.model-info-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.model-icon {
  width: 40px;
  height: 40px;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.model-info-content {
  flex: 1;
  min-width: 0;
}

.model-name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
  line-height: var(--leading-tight);
}

.model-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--leading-normal);
}

.model-stats {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--space-lg);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-sm);
}

.stat-label {
  color: var(--text-secondary);
}

.stat-value {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.stat-value.ready {
  color: var(--color-success);
}

/* Settings Grid */
.settings-grid {
  display: grid;
  gap: var(--space-xl);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  height: 40px;
  padding: 0 var(--space-xl);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
  background: var(--bg-primary);
  border-color: var(--border-secondary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-sm);
  margin-top: var(--space-xl);
}

.action-buttons .btn {
  flex: 1;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  line-height: var(--leading-tight);
}

.stat-card .stat-label {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: var(--space-xs);
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;
}

/* Chat Header */
.chat-header {
  height: var(--header-height);
  padding: 0 var(--space-2xl);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
  box-shadow: var(--shadow-xs);
}

.chat-title-section {
  flex: 1;
}

.chat-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
  line-height: var(--leading-tight);
}

.chat-subtitle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.model-indicator {
  font-weight: var(--font-medium);
}

.separator {
  color: var(--text-muted);
}

.connection-indicator {
  font-weight: var(--font-medium);
}

.connection-indicator.connected {
  color: var(--color-success);
}

.connection-indicator.disconnected {
  color: var(--color-error);
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.messages-scroll {
  height: 100%;
  overflow-y: auto;
  padding: var(--space-2xl);
}

/* Welcome Message */
.welcome-message {
  max-width: 600px;
  margin: var(--space-5xl) auto;
  text-align: center;
}

.welcome-icon {
  width: 80px;
  height: 80px;
  background: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-2xl) auto;
}

.welcome-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-lg) 0;
  line-height: var(--leading-tight);
}

.welcome-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0 0 var(--space-3xl) 0;
  line-height: var(--leading-relaxed);
}

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  max-width: 400px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  font-size: var(--text-base);
  color: var(--text-secondary);
  text-align: left;
}

.feature-item svg {
  color: var(--color-success);
  flex-shrink: 0;
}

/* Message Styles */
.message {
  max-width: 70%;
  margin-bottom: var(--space-xl);
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  margin-left: auto;
}

.message.assistant {
  margin-right: auto;
}

.message-content {
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--radius-2xl);
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  word-wrap: break-word;
}

.message.user .message-content {
  background: var(--color-primary);
  color: var(--text-inverse);
  border-bottom-right-radius: var(--radius-md);
}

.message.assistant .message-content {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-bottom-left-radius: var(--radius-md);
}

.message.system .message-content {
  background: var(--color-warning);
  color: var(--text-inverse);
  text-align: center;
  font-size: var(--text-sm);
  margin: 0 auto;
  max-width: 80%;
}

.message.error .message-content {
  background: var(--color-error);
  color: var(--text-inverse);
  text-align: center;
  font-size: var(--text-sm);
  margin: 0 auto;
  max-width: 80%;
}

/* Typing Indicator */
.typing-indicator {
  max-width: 70%;
  margin-bottom: var(--space-xl);
  margin-right: auto;
  animation: messageSlideIn 0.3s ease-out;
}

.typing-content {
  padding: var(--space-lg) var(--space-xl);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  border-bottom-left-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.typing-dots {
  display: flex;
  gap: var(--space-xs);
}

.typing-dot {
  width: 8px;
  height: 8px;
  background: var(--text-tertiary);
  border-radius: var(--radius-full);
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typingAnimation {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* Input Area */
.input-area {
  padding: var(--space-2xl);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-primary);
}

.input-container {
  max-width: 1000px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
}

.input-field-container {
  flex: 1;
  position: relative;
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  transition: all var(--transition-fast);
}

.input-field-container:focus-within {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1);
}

.message-input {
  width: 100%;
  min-height: 56px;
  max-height: 200px;
  padding: var(--space-lg) var(--space-xl);
  padding-right: 100px;
  border: none;
  background: transparent;
  font-size: var(--text-base);
  font-family: var(--font-family-sans);
  color: var(--text-primary);
  resize: none;
  outline: none;
  line-height: var(--leading-normal);
}

.message-input::placeholder {
  color: var(--text-muted);
}

.message-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-actions {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.input-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.input-action-btn:hover:not(:disabled) {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.input-action-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.send-button {
  width: 56px;
  height: 56px;
  border: none;
  background: var(--color-primary);
  color: var(--text-inverse);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg\
