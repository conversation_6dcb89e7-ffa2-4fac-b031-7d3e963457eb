<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Chat</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>

<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <h1>Olla<PERSON> Chat</h1>
            <div class="header-controls">
                <select id="modelSelect" class="model-select">
                    <option value="">Select Model...</option>
                </select>
                <button id="historyBtn" class="history-btn">History</button>
                <button id="mcpBtn" class="mcp-btn">MCP</button>
            </div>
        </header>

        <!-- Chat Area -->
        <main class="chat-container">
            <div id="messages" class="messages"></div>
        </main>

        <!-- Input Area -->
        <footer class="input-area">
            <div class="input-container">
                <div class="input-row">
                    <textarea id="messageInput" placeholder="Type your message..." rows="1"></textarea>
                    <div class="input-buttons">
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        <button id="attachBtn" class="attach-btn" title="Attach Image">📎</button>
                        <button id="searchBtn" class="search-btn" title="Search with MCP">🔍</button>
                        <button id="sendBtn" class="send-btn">Send</button>
                    </div>
                </div>
                <div id="imagePreview" class="image-preview" style="display: none;"></div>
            </div>
        </footer>
    </div>

    <!-- MCP Modal -->
    <div id="mcpModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>MCP Servers</h2>
                <button id="closeMcpModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="add-server-section">
                    <h3>Add New MCP Server</h3>
                    <div class="add-server-form">
                        <input type="text" id="serverUrl" placeholder="Enter Smithery.ai MCP server URL"
                            class="server-input">
                        <input type="text" id="serverName" placeholder="Server name (optional)" class="server-input">
                        <button id="addServerBtn" class="add-server-btn">Add Server</button>
                    </div>
                    <div class="server-help">
                        <p><strong>How to add MCP servers:</strong></p>
                        <ol>
                            <li>Visit <a href="https://smithery.ai" target="_blank">smithery.ai</a></li>
                            <li>Browse available MCP servers</li>
                            <li>Copy the server URL</li>
                            <li>Paste it above and click "Add Server"</li>
                        </ol>
                    </div>
                </div>
                <div id="mcpServers" class="mcp-servers"></div>
            </div>
        </div>
    </div>

    <!-- Chat History Modal -->
    <div id="historyModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Chat History</h2>
                <button id="closeHistoryModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <button id="clearHistoryBtn" class="clear-history-btn">Clear History</button>
                </div>
                <div id="historyList" class="history-list"></div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>

</html>