<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Chat</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <h1><PERSON>lla<PERSON> Chat</h1>
            <div class="header-controls">
                <select id="modelSelect" class="model-select">
                    <option value="">Select Model...</option>
                </select>
                <button id="mcpBtn" class="mcp-btn">MCP</button>
            </div>
        </header>

        <!-- Chat Area -->
        <main class="chat-container">
            <div id="messages" class="messages"></div>
        </main>

        <!-- Input Area -->
        <footer class="input-area">
            <div class="input-container">
                <textarea id="messageInput" placeholder="Type your message..." rows="1"></textarea>
                <button id="sendBtn" class="send-btn">Send</button>
            </div>
        </footer>
    </div>

    <!-- MCP Modal -->
    <div id="mcpModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>MCP Servers</h2>
                <button id="closeMcpModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div id="mcpServers" class="mcp-servers"></div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
